/*
This client component provides the header for the app.
*/

"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  SignedIn,
  SignedOut,
  SignInButton,
  SignUpButton,
  UserButton
} from "@clerk/nextjs"
import { motion } from "framer-motion"
import { Menu, Receipt, X, ArrowRight, LayoutDashboard } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useLoading } from "@/contexts/loading-context"
import ResourcesDropdown from "./resources-dropdown"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { BRAND } from "@/lib/constants"

const navLinks = [{ href: "/pricing", label: "Pricing" }]

// Resource links are now defined in the ResourcesDropdown component

const signedInLinks = [{ href: "/dashboard", label: "Dashboard" }]

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const router = useRouter()
  const { startLoading } = useLoading()

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  // Handle dashboard navigation with loading overlay
  const handleDashboardClick = (
    e: React.MouseEvent<HTMLAnchorElement>,
    path: string
  ) => {
    e.preventDefault()
    startLoading()

    // Add a small delay before navigation to ensure the loading overlay appears
    setTimeout(() => {
      router.push(path)
    }, 100) // 100ms delay (reduced from 300ms)
  }

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className={`sticky top-0 z-50 transition-colors ${
        isScrolled
          ? "bg-background/80 shadow-sm backdrop-blur-sm"
          : "bg-background"
      }`}
    >
      <div className="container mx-auto flex max-w-7xl items-center justify-between p-4">
        <motion.div
          className="flex items-center hover:cursor-pointer hover:opacity-80"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Link
            href="/"
            className="from-primary bg-gradient-to-r to-blue-600 bg-clip-text text-xl font-bold text-transparent"
            prefetch={false}
          >
            {BRAND.NAME}
          </Link>
        </motion.div>

        <nav className="absolute left-1/2 hidden -translate-x-1/2 items-center space-x-4 md:flex">
          {navLinks.map(link => (
            <motion.div
              key={link.href}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href={link.href}
                className="text-muted-foreground hover:text-foreground rounded-full px-3 py-1 transition"
                prefetch={false}
              >
                {link.label}
              </Link>
            </motion.div>
          ))}

          <ResourcesDropdown />
        </nav>

        <div className="flex items-center space-x-4">
          <SignedOut>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/dashboard"
                    prefetch={false}
                    onClick={e => handleDashboardClick(e, "/dashboard")}
                  >
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        className="from-primary hover:from-primary/90 bg-gradient-to-r to-blue-600 shadow-sm hover:to-blue-700"
                        type="button"
                      >
                        Dashboard <ArrowRight className="ml-1 size-4" />
                      </Button>
                    </motion.div>
                  </Link>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Access your experiments and optimization tools</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </SignedOut>

          <SignedIn>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/dashboard/home"
                    prefetch={false}
                    onClick={e => handleDashboardClick(e, "/dashboard/home")}
                  >
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        className="from-primary hover:from-primary/90 bg-gradient-to-r to-blue-600 shadow-sm hover:to-blue-700"
                        type="button"
                      >
                        Dashboard <ArrowRight className="ml-1 size-4" />
                      </Button>
                    </motion.div>
                  </Link>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Access your experiments and optimization tools</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <UserButton />
          </SignedIn>

          <motion.div
            className="md:hidden"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMenu}
              aria-label="Toggle menu"
              type="button"
            >
              {isMenuOpen ? (
                <X className="size-6" />
              ) : (
                <Menu className="size-6" />
              )}
            </Button>
          </motion.div>
        </div>
      </div>

      {isMenuOpen && (
        <motion.nav
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="bg-primary-foreground text-primary p-4 md:hidden"
        >
          <ul className="space-y-2">
            <li>
              <Link
                href="/"
                className="block hover:underline"
                onClick={toggleMenu}
                prefetch={false}
              >
                Home
              </Link>
            </li>
            {navLinks.map(link => (
              <li key={link.href}>
                <Link
                  href={link.href}
                  className="block hover:underline"
                  onClick={toggleMenu}
                  prefetch={false}
                >
                  {link.label}
                </Link>
              </li>
            ))}

            <li className="pt-2 font-medium">Resources</li>
            {/* Import resource links from the ResourcesDropdown component */}
            <li className="pl-2">
              <Link
                href="/tutorials"
                className="block hover:underline"
                onClick={toggleMenu}
                prefetch={false}
              >
                Tutorials
              </Link>
            </li>
            {/* Case Studies - Hidden as requested */}
            {/*
            <li className="pl-2">
              <Link
                href="/case-studies"
                className="block hover:underline"
                onClick={toggleMenu}
                prefetch={false}
              >
                Case Studies
              </Link>
            </li>
            */}
            <li className="pl-2">
              <Link
                href="/features"
                className="block hover:underline"
                onClick={toggleMenu}
                prefetch={false}
              >
                Features
              </Link>
            </li>

            <SignedIn>
              {signedInLinks.map(link => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="block hover:underline"
                    onClick={e => {
                      toggleMenu()
                      handleDashboardClick(e, "/dashboard/home")
                    }}
                    prefetch={false}
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </SignedIn>
          </ul>
        </motion.nav>
      )}
    </motion.header>
  )
}
