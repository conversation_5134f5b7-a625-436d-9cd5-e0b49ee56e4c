// actions/suggestion-workflow-actions.ts
"use server"

import { auth } from "@clerk/nextjs/server"
import { getSuggestion } from "./optimization-actions"
import { getOptimizationByOptimizerIdAction } from "./db/optimizations-actions"
import {
  createSuggestionsAction,
  getSuggestionsByStatusAction,
  updateSuggestionStatusAction,
  updateSuggestionTargetValuesAction
} from "./db/suggestions-actions"
import { InsertSuggestion } from "@/db/schema"
import { getSubscriptionAction } from "./subscription-actions"

export type ActionState<T = undefined> = {
  isSuccess: boolean
  message: string
  data?: T
}

/**
 * Gets suggestions from the API and stores them in the database
 */
export async function getSuggestionWorkflowAction(
  optimizationId: string,
  batchSize: number = 1
): Promise<ActionState<{ suggestions: any[], batchId: string | null }>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to get suggestions"
    }
  }

  try {
    // Check user's subscription tier
    const subscription = await getSubscriptionAction()
    console.log(`[getSuggestionWorkflowAction] User subscription tier: ${subscription.tier}, status: ${subscription.status}`)

    // Only block users who are definitively on the "free" tier
    // Allow "trial" and "pro" users to proceed
    if (subscription.tier === "free") {
      console.log(`[getSuggestionWorkflowAction] Blocking free tier user from getting suggestions`)
      return {
        isSuccess: false,
        message: "This feature is only available for trial and premium users. Please upgrade your subscription to use AI-powered suggestions."
      }
    }

    console.log(`[getSuggestionWorkflowAction] User has ${subscription.tier} tier, proceeding with suggestions`)

    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)

    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      }
    }

    // Always generate a batch ID, regardless of batch size
    // This ensures that all measurements from the same batch share the same batch ID
    // We use a consistent format: batch_<optimizationId>_<timestamp>
    const batchId = `batch_${optimizationId}_${Date.now()}`
    console.log(`Generated batch ID: ${batchId} for batch size ${batchSize}`)

    // Get suggestion from the API
    const apiResult = await getSuggestion(optimizationId, batchSize)

    if (!apiResult.isSuccess || !apiResult.data) {
      return {
        isSuccess: false,
        message: `API Error: ${apiResult.message}`
      }
    }

    // Store the suggestions in the database
    try {
      const suggestions = apiResult.data.suggestions
      const dbSuggestions: InsertSuggestion[] = suggestions.map((suggestion, index) => ({
        optimizationId: optResult.data!.id,
        parameters: suggestion,
        batchId,
        suggestionIndex: index,
        status: "pending",
        targetValues: {}
      }))

      // Save suggestions to the database
      const dbResult = await createSuggestionsAction(dbSuggestions)
      if (!dbResult.isSuccess) {
        console.error("Failed to store suggestions in database:", dbResult.message)
        // Continue anyway - we'll return the API suggestions even if DB storage fails
      } else {
        console.log(`Stored ${dbSuggestions.length} suggestions in database with batch ID ${batchId}`)

        // Add database IDs to the suggestions for reference
        if (dbResult.data) {
          for (let i = 0; i < suggestions.length; i++) {
            suggestions[i] = {
              ...suggestions[i],
              _suggestionId: dbResult.data[i].id,
              _targetValues: {}
            }
          }
        }
      }
    } catch (dbError) {
      console.error("Database error while storing suggestions:", dbError)
      // Continue anyway - we'll return the API suggestions even if DB storage fails

      // Add client-side IDs to the suggestions for reference
      apiResult.data.suggestions = apiResult.data.suggestions.map((suggestion, index) => ({
        ...suggestion,
        _suggestionId: `${batchId}_${index}`, // Create a client-side ID
        _targetValues: {} // Initialize empty target values
      }))
    }

    return {
      isSuccess: true,
      message: "Got suggestions successfully",
      data: {
        suggestions: apiResult.data.suggestions,
        batchId: batchId
      }
    }

  } catch (error) {
    console.error("Error in workflow:", error)
    return {
      isSuccess: false,
      message: "Failed to get suggestions"
    }
  }
}

/**
 * Gets pending suggestions from the database
 */
export async function getPendingSuggestionsWorkflowAction(
  optimizationId: string
): Promise<ActionState<{ suggestions: any[], batchId: string | null }>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to get suggestions"
    }
  }

  try {
    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)

    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      }
    }

    try {
      // Get saved, pending, and in_progress suggestions from the database
      console.log(`Getting suggestions for optimization ID: ${optResult.data!.id}`)
      const savedResult = await getSuggestionsByStatusAction(optResult.data!.id, "saved")
      console.log(`Saved suggestions result: ${savedResult.isSuccess ? savedResult.data?.length || 0 : 'error'} suggestions`)

      const pendingResult = await getSuggestionsByStatusAction(optResult.data!.id, "pending")
      console.log(`Pending suggestions result: ${pendingResult.isSuccess ? pendingResult.data?.length || 0 : 'error'} suggestions`)

      const inProgressResult = await getSuggestionsByStatusAction(optResult.data!.id, "in_progress")
      console.log(`In-progress suggestions result: ${inProgressResult.isSuccess ? inProgressResult.data?.length || 0 : 'error'} suggestions`)

      if ((!savedResult.isSuccess || !savedResult.data || savedResult.data.length === 0) &&
          (!pendingResult.isSuccess || !pendingResult.data || pendingResult.data.length === 0) &&
          (!inProgressResult.isSuccess || !inProgressResult.data || inProgressResult.data.length === 0)) {
        return {
          isSuccess: true,
          message: "No saved, pending, or in-progress suggestions found",
          data: {
            suggestions: [],
            batchId: null
          }
        }
      }

      // Combine suggestions, prioritizing saved ones
      // Filter out any suggestions that might have been marked as submitted
      const allSuggestionsBeforeFilter = [
        ...(savedResult.data || []),
        ...(inProgressResult.data || []),
        ...(pendingResult.data || [])
      ];

      console.log(`Total suggestions before filtering: ${allSuggestionsBeforeFilter.length}`);

      // Log the status of each suggestion for debugging
      const statusCounts = allSuggestionsBeforeFilter.reduce<Record<string, number>>((acc, suggestion) => {
        acc[suggestion.status] = (acc[suggestion.status] || 0) + 1;
        return acc;
      }, {});
      console.log(`Status counts before filtering:`, statusCounts);

      // Filter out submitted suggestions
      const allSuggestions = allSuggestionsBeforeFilter.filter(suggestion => {
        const isSubmitted = suggestion.status === "submitted";
        if (isSubmitted) {
          console.log(`Filtering out submitted suggestion: ${suggestion.id}`);
        }
        return !isSubmitted;
      });

      console.log(`Total suggestions after filtering: ${allSuggestions.length}`);

      // Group suggestions by batch ID
      const suggestionsByBatch = allSuggestions.reduce((acc, suggestion) => {
        if (!acc[suggestion.batchId]) {
          acc[suggestion.batchId] = []
        }
        const params = suggestion.parameters as Record<string, any>;
        acc[suggestion.batchId].push({
          ...params,
          _suggestionId: suggestion.id, // Add the suggestion ID for reference
          _targetValues: suggestion.targetValues || {}, // Add any saved target values
          _status: suggestion.status // Add status for UI differentiation
        })
        return acc
      }, {} as Record<string, any[]>)

      // Use the most recent batch
      const batchIds = Object.keys(suggestionsByBatch)
      if (batchIds.length === 0) {
        return {
          isSuccess: true,
          message: "No pending or in-progress suggestions found",
          data: {
            suggestions: [],
            batchId: null
          }
        }
      }

      // Sort batch IDs by timestamp (newest first)
      batchIds.sort((a, b) => {
        const aTime = parseInt(a.split('_')[2] || '0')
        const bTime = parseInt(b.split('_')[2] || '0')
        return bTime - aTime
      })

      const latestBatchId = batchIds[0]
      const suggestions = suggestionsByBatch[latestBatchId]

      return {
        isSuccess: true,
        message: "Got pending and in-progress suggestions successfully",
        data: {
          suggestions,
          batchId: latestBatchId
        }
      }
    } catch (dbError) {
      console.error("Database error while getting pending suggestions:", dbError)
      // Return empty result if database fails
      return {
        isSuccess: true,
        message: "No pending suggestions found (database error)",
        data: {
          suggestions: [],
          batchId: null
        }
      }
    }
  } catch (error) {
    console.error("Error in workflow:", error)
    return {
      isSuccess: false,
      message: "Failed to get pending suggestions"
    }
  }
}

/**
 * Checks if there are saved suggestions for an optimization without loading them
 */
export async function checkSavedSuggestionsWorkflowAction(
  optimizationId: string
): Promise<ActionState<{ hasSavedSuggestions: boolean, count: number }>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to check for saved suggestions"
    }
  }

  try {
    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)

    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      }
    }

    try {
      // Get saved suggestions from the database
      console.log(`Checking for saved suggestions for optimization ID: ${optResult.data!.id}`)
      const savedResult = await getSuggestionsByStatusAction(optResult.data!.id, "saved")

      if (!savedResult.isSuccess || !savedResult.data) {
        return {
          isSuccess: true,
          message: "No saved suggestions found or error checking",
          data: {
            hasSavedSuggestions: false,
            count: 0
          }
        }
      }

      return {
        isSuccess: true,
        message: `Found ${savedResult.data.length} saved suggestions`,
        data: {
          hasSavedSuggestions: savedResult.data.length > 0,
          count: savedResult.data.length
        }
      }
    } catch (dbError) {
      console.error("Database error while checking for saved suggestions:", dbError)
      return {
        isSuccess: true,
        message: "Error checking for saved suggestions",
        data: {
          hasSavedSuggestions: false,
          count: 0
        }
      }
    }
  } catch (error) {
    console.error("Error in workflow:", error)
    return {
      isSuccess: false,
      message: "Failed to check for saved suggestions",
      data: {
        hasSavedSuggestions: false,
        count: 0
      }
    }
  }
}

/**
 * Gets saved suggestions from the database
 */
export async function getSavedSuggestionsWorkflowAction(
  optimizationId: string
): Promise<ActionState<{ suggestions: any[], batchId: string | null }>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to get saved suggestions"
    }
  }

  try {
    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)

    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      }
    }

    try {
      // Get saved suggestions from the database
      console.log(`Getting saved suggestions for optimization ID: ${optResult.data!.id}`)
      const savedResult = await getSuggestionsByStatusAction(optResult.data!.id, "saved")

      if (!savedResult.isSuccess || !savedResult.data || savedResult.data.length === 0) {
        return {
          isSuccess: true,
          message: "No saved suggestions found",
          data: {
            suggestions: [],
            batchId: null
          }
        }
      }

      // Group suggestions by batch ID
      const suggestionsByBatch: Record<string, any[]> = {}

      for (const suggestion of savedResult.data) {
        if (!suggestionsByBatch[suggestion.batchId]) {
          suggestionsByBatch[suggestion.batchId] = []
        }

        // Convert database suggestion to API format
        const params = suggestion.parameters as Record<string, any>;
        const apiSuggestion = {
          ...params,
          _suggestionId: suggestion.id,
          _targetValues: suggestion.targetValues || {}
        }

        suggestionsByBatch[suggestion.batchId].push(apiSuggestion)
      }

      // Sort batch IDs by timestamp (newest first)
      const batchIds = Object.keys(suggestionsByBatch)
      if (batchIds.length === 0) {
        return {
          isSuccess: true,
          message: "No saved suggestions found after processing",
          data: {
            suggestions: [],
            batchId: null
          }
        }
      }

      batchIds.sort((a, b) => {
        const aTime = parseInt(a.split('_')[2] || '0')
        const bTime = parseInt(b.split('_')[2] || '0')
        return bTime - aTime
      })

      const latestBatchId = batchIds[0]
      const suggestions = suggestionsByBatch[latestBatchId]

      return {
        isSuccess: true,
        message: "Got saved suggestions successfully",
        data: {
          suggestions,
          batchId: latestBatchId
        }
      }
    } catch (dbError) {
      console.error("Database error while getting saved suggestions:", dbError)
      return {
        isSuccess: true,
        message: "No saved suggestions found (database error)",
        data: {
          suggestions: [],
          batchId: null
        }
      }
    }
  } catch (error) {
    console.error("Error in workflow:", error)
    return {
      isSuccess: false,
      message: "Failed to get saved suggestions"
    }
  }
}

/**
 * Marks a suggestion as submitted
 */
export async function markSuggestionSubmittedAction(
  suggestionId: string
): Promise<ActionState<void>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to update suggestions"
    }
  }

  try {
    // Check if this is a database ID or a client-side ID
    if (suggestionId.includes('_')) {
      // This is a client-side ID, we can't update it in the database
      console.log(`Marking client-side suggestion ${suggestionId} as submitted (no database update)`)
      return {
        isSuccess: true,
        message: "Suggestion marked as submitted (client-side only)"
      }
    }

    try {
      // Update the suggestion status in the database
      // This ensures the suggestion won't be loaded again on next visit
      console.log(`Marking suggestion ${suggestionId} as submitted in database`)
      const result = await updateSuggestionStatusAction(suggestionId, "submitted")
      if (!result.isSuccess) {
        console.warn(`Failed to mark suggestion as submitted: ${result.message}`)
        // Return success anyway - the UI still works
        return {
          isSuccess: true,
          message: "Suggestion marked as submitted (UI only)"
        }
      }

      console.log(`Successfully marked suggestion ${suggestionId} as submitted`)
      return {
        isSuccess: true,
        message: "Suggestion marked as submitted"
      }
    } catch (dbError) {
      console.error("Database error while marking suggestion as submitted:", dbError)
      // Return success anyway - the UI still works
      return {
        isSuccess: true,
        message: "Suggestion marked as submitted (UI only)"
      }
    }
  } catch (error) {
    console.error("Error in workflow:", error)
    return {
      isSuccess: false,
      message: "Failed to mark suggestion as submitted"
    }
  }
}

// markSuggestionDiscardedAction function removed

/**
 * Updates a suggestion's target values
 */
export async function updateSuggestionTargetValuesWorkflowAction(
  suggestionId: string,
  targetValues: Record<string, string>
): Promise<ActionState<void>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to update suggestions"
    }
  }

  try {
    // Check if this is a database ID or a client-side ID
    if (suggestionId.includes('_')) {
      // This is a client-side ID, we can't update it in the database
      console.log(`Updating target values for client-side suggestion ${suggestionId} (no database update)`)
      return {
        isSuccess: true,
        message: "Target values updated (client-side only)"
      }
    }

    try {
      // Update the suggestion target values in the database
      const result = await updateSuggestionTargetValuesAction(suggestionId, targetValues)
      if (!result.isSuccess) {
        console.warn(`Failed to update target values: ${result.message}`)
        // Return success anyway - the UI still works
        return {
          isSuccess: true,
          message: "Target values updated (UI only)"
        }
      }

      return {
        isSuccess: true,
        message: "Target values updated"
      }
    } catch (dbError) {
      console.error("Database error while updating target values:", dbError)
      // Return success anyway - the UI still works
      return {
        isSuccess: true,
        message: "Target values updated (UI only)"
      }
    }
  } catch (error) {
    console.error("Error in workflow:", error)
    return {
      isSuccess: false,
      message: "Failed to update target values"
    }
  }
}

/**
 * Explicitly saves suggestions for later use
 */
export async function saveSuggestionsWorkflowAction(
  optimizationId: string,
  suggestions: any[],
  batchId: string | null
): Promise<ActionState<void>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to save suggestions"
    }
  }

  try {
    console.log(`Saving ${suggestions.length} suggestions for optimization ${optimizationId}`)

    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)

    if (!optResult.isSuccess || !optResult.data) {
      console.error(`Database Error: ${optResult.message}`)
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      }
    }

    // Generate a batch ID if none exists
    const saveBatchId = batchId || `batch_${optimizationId}_${Date.now()}`
    console.log(`Saving suggestions with batch ID: ${saveBatchId}`)

    // Check if these suggestions are already in the database
    const existingSuggestions = suggestions.filter(s => s._suggestionId && !s._suggestionId.includes('_'))
    const newSuggestions = suggestions.filter(s => !s._suggestionId || s._suggestionId.includes('_'))

    console.log(`Found ${existingSuggestions.length} existing suggestions and ${newSuggestions.length} new suggestions`)

    // For existing suggestions, update their status to "saved"
    for (const suggestion of existingSuggestions) {
      try {
        console.log(`Updating suggestion ${suggestion._suggestionId} status to saved`)
        const updateResult = await updateSuggestionStatusAction(suggestion._suggestionId, "saved")
        if (updateResult.isSuccess) {
          console.log(`Successfully updated suggestion ${suggestion._suggestionId} status to saved`)
        } else {
          console.error(`Failed to update suggestion ${suggestion._suggestionId}:`, updateResult.message)
        }
      } catch (error) {
        console.error(`Error updating suggestion ${suggestion._suggestionId}:`, error)
        // Continue with other suggestions
      }
    }

    // For new suggestions, create them in the database with "saved" status
    if (newSuggestions.length > 0) {
      console.log(`Creating ${newSuggestions.length} new saved suggestions`)

      // Clean up the suggestion objects to avoid circular references
      const cleanedSuggestions = newSuggestions.map(suggestion => {
        // Extract only the parameter values (not metadata)
        const parameters: Record<string, any> = {};
        for (const [key, value] of Object.entries(suggestion)) {
          if (!key.startsWith('_')) {
            parameters[key] = value;
          }
        }
        return parameters;
      });

      const dbSuggestions: InsertSuggestion[] = cleanedSuggestions.map((suggestion, index) => ({
        optimizationId: optResult.data!.id,
        parameters: suggestion,
        batchId: saveBatchId,
        suggestionIndex: index,
        status: "saved",
        targetValues: newSuggestions[index]._targetValues || {}
      }))

      try {
        console.log("Creating suggestions in database:", dbSuggestions)
        const createResult = await createSuggestionsAction(dbSuggestions)
        if (createResult.isSuccess) {
          console.log(`Successfully created ${dbSuggestions.length} new saved suggestions`)
        } else {
          console.error("Failed to create new suggestions:", createResult.message)
        }
      } catch (error) {
        console.error("Error creating new suggestions:", error)
      }
    }

    return {
      isSuccess: true,
      message: "Suggestions saved successfully"
    }
  } catch (error) {
    console.error("Error saving suggestions:", error)
    return {
      isSuccess: false,
      message: "Failed to save suggestions"
    }
  }
}
